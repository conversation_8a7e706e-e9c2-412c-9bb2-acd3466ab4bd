import 'package:flutter_kit/src/core/routing/app_router.dart';
import 'package:flutter_kit/src/datasource/http/dio_config.dart';
import 'package:flutter_kit/src/datasource/http/api_service.dart';
import 'package:flutter_kit/src/datasource/repositories/base_info_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/base_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/collection_job_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/enterprise_detail_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/home_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/job_detail_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/location_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/login_repository.dart';
import 'package:flutter_kit/src/datasource/repositories/profile_repository.dart';
import 'package:flutter_kit/src/features/base_info/logic/base_info_logic.dart';
import 'package:flutter_kit/src/features/discover/logic/discover_logic.dart';
import 'package:flutter_kit/src/features/follow_company/logic/follow_company_logic.dart';
import 'package:flutter_kit/src/features/home/<USER>/home_logic.dart';
import 'package:flutter_kit/src/features/location/logic/location_logic.dart';
import 'package:flutter_kit/src/features/location/services/location_cache_service.dart';
import 'package:flutter_kit/src/features/login/logic/examinee_helper.dart';
import 'package:flutter_kit/src/features/main_tab/logic/main_tab_logic.dart';
import 'package:flutter_kit/src/features/messages/logic/messages_logic.dart';
import 'package:flutter_kit/src/features/profile/logic/profile_logic.dart';
import 'package:flutter_kit/src/features/resume/logic/resume_logic.dart';
import 'package:flutter_kit/src/features/splash/logic/splash_logic.dart';

import 'package:flutter_kit/src/shared/services/app_logger.dart';
import 'package:flutter_kit/src/shared/services/storage/local_storage.dart';
import 'package:flutter_kit/src/shared/services/storage/storage.dart';
import 'package:get_it/get_it.dart';

import '../datasource/repositories/follow_company_repository.dart';


final GetIt locator = GetIt.instance
  ..registerLazySingleton(() => DioConfig())
  ..registerLazySingleton(() => AppRouter())
  ..registerLazySingleton<AppLogger>(() => AppLogger())
  ..registerLazySingleton<Storage>(() => LocalStorage())
  ..registerLazySingleton(() => ApiService(dio: locator<DioConfig>().dio))
  ..registerLazySingleton(() => LocationCacheService()) // 注册LocationCacheService
  ..registerLazySingleton(() => HomeReportsitory())
  ..registerLazySingleton(() => JobDetailRepository())
  ..registerLazySingleton(()=>EnterpriseDetailRepository())
  ..registerLazySingleton(() => ProfileRepository())
  ..registerLazySingleton(() => BaseInfoRepository()) // 注册BaseInfoRepository
  ..registerLazySingleton(() => LocationRepository()) // 注册LocationRepository
  ..registerFactory(() => HomeLogic(repository: locator<HomeReportsitory>()))
  ..registerFactory(() => MainTabLogic())
  ..registerFactory(() => DiscoverLogic())
  ..registerFactory(() => MessagesLogic())
  ..registerFactory(()=>ResumeLogic())
  ..registerFactory(() => ProfileLogic(repository: locator<ProfileRepository>()))
  ..registerFactory(()=>LoginRepository())
  ..registerFactory(()=>BaseInfoLogic(repository: locator<BaseInfoRepository>()))
  ..registerLazySingleton(() => ExamineeHelper())
  ..registerFactory(()=>CollectionJobRepository())

  ..registerFactory(()=>FollowCompanyRepository())
  ..registerFactory(()=>FollowCompanyLogic(repository: locator<FollowCompanyRepository>()))


  ..registerFactory(() => SplashLogic());
