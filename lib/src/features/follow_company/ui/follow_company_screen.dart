

import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/core/theme/app_color.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import '../../../core/routing/app_router.dart';
import '../logic/follow_company_logic.dart';
import 'widget/follow_company_item.dart';
import '../../../base/base.dart';
import '../../../shared/locator.dart';

@RoutePage()
class FollowCompanyScreen extends ViewStateWidget<FollowCompanyLogic> {
  const FollowCompanyScreen({super.key});

  @override
  Widget? buildCustomAppBar(BuildContext context, FollowCompanyLogic logic) {
    return customTitleBar(context, '关注企业', leftCallback: () {
      context.router.pop();
    });
  }

  @override
  Widget buildBody(BuildContext context, FollowCompanyLogic logic) {
    // 🔥 为CustomListLogic提供Provider
    return ChangeNotifierProvider<CustomListLogic<CollectEnterpriseEntity>>.value(
      value: logic.listLogic,
      child: _buildContent(context, logic),
    );
  }

  /// 🔥 构建页面内容
  Widget _buildContent(BuildContext context, FollowCompanyLogic logic) {
    return Container(
      color: ResumeTheme.backgroundColor,
      child: CustomListView<CollectEnterpriseEntity>(
        dataSource: logic.dataSource,
        externalLogic: logic.listLogic,
        customFooter: _buildContentFooter(context),
        itemBuilder: (context, item, index) {
          return FollowCompanyItem(
            company: item,
            index: index,
            onUnfollow: () => _handleUnfollow(context, logic, item),
            onTap: () => _handleCompanyTap(context, item),
          );
        },
        enableRefresh: true,
        enableLoadMore: true,
        enableItemAnimation: false,
        emptyWidget: _buildEmptyWidget(),
        errorWidget: _buildErrorWidget(logic),
        padding: EdgeInsets.symmetric(vertical: 8.h),
      ),
    );
  }

  /// 🔥 处理取消关注
  void _handleUnfollow(BuildContext context, FollowCompanyLogic logic, CollectEnterpriseEntity company) {

  }

  Widget _buildContentFooter(BuildContext context){
    return Center(
      child: Text('-  我是有底线的  -',style: TextStyle(color: AppColors.color_666666,fontSize: 10.w),),
    );
  }


  /// 🔥 处理企业点击
  void _handleCompanyTap(BuildContext context, CollectEnterpriseEntity company) {
    context.router.push(EnterpriseDetailRoute(entId: company.enterpriseId));
  }

  /// 🔥 构建空数据状态组件
  Widget _buildEmptyWidget() {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.business_outlined,
            size: 64.w,
            color: ResumeTheme.textSecondary,
          ),
          SizedBox(height: 16.h),
          Text(
            '暂无关注的企业',
            style: TextStyle(
              fontSize: 16.w,
              color: ResumeTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '快去关注感兴趣的企业吧',
            style: TextStyle(
              fontSize: 14.w,
              color: ResumeTheme.textSecondary.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  /// 🔥 构建错误状态组件
  Widget _buildErrorWidget(FollowCompanyLogic logic) {
    return Container(
      padding: EdgeInsets.all(40.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64.w,
            color: Colors.red.shade400,
          ),
          SizedBox(height: 16.h),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 16.w,
              color: ResumeTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '网络连接异常，请检查网络设置',
            style: TextStyle(
              fontSize: 14.w,
              color: ResumeTheme.textSecondary.withOpacity(0.8),
            ),
          ),
          SizedBox(height: 16.h),
          ElevatedButton(
            onPressed: () => logic.refreshPaging(),
            style: ElevatedButton.styleFrom(
              backgroundColor: ResumeTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  @override
  FollowCompanyLogic createController() {
    return locator<FollowCompanyLogic>();
  }
}