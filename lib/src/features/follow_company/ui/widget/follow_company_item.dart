import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';
import 'package:flutter_kit/src/features/resume/theme/resume_theme.dart';
import 'package:flutter_svg/svg.dart';

/// 🔥 关注企业列表项组件
///
/// 功能特性：
/// - ✅ 展示企业基本信息
/// - ✅ 支持取消关注操作
/// - ✅ 使用ResumeTheme配色方案
/// - ✅ 响应式布局设计
class FollowCompanyItem extends StatelessWidget {
  final CollectEnterpriseEntity company;
  final int index;
  final VoidCallback? onUnfollow;
  final VoidCallback? onTap;

  const FollowCompanyItem({
    super.key,
    required this.company,
    required this.index,
    this.onUnfollow,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Material(
        color: Colors.transparent,
        child: GestureDetector(
          onTap: onTap,
          child: Padding(
            padding: EdgeInsets.all(16.w),
            child: Row(
              children: [
                // 企业图标
                _buildCompanyIcon(),
                SizedBox(width: 12.w),
                // 企业信息
                Expanded(child: _buildCompanyInfo()),
                // 取消关注按钮
                // _buildUnfollowButton(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建企业图标
  Widget _buildCompanyIcon() {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        color: ResumeTheme.primaryColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: SvgPicture.asset(
        'assets/images/company_default.svg',
        width: 30,
        height: 30,
        fit: BoxFit.cover,
      )
    );
  }

  /// 构建企业信息
  Widget _buildCompanyInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 企业名称
        Text(
          company.enterpriseName.isNotEmpty ? company.enterpriseName : '未知企业',
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w600,
            color: ResumeTheme.textPrimary,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        SizedBox(height: 4.h),
        // 关注时间
        Text(
          _formatCollectTime(company.collectTime),
          style: TextStyle(
            fontSize: 12.sp,
            color: ResumeTheme.textSecondary,
          ),
        ),
      ],
    );
  }

  /// 构建取消关注按钮
  Widget _buildUnfollowButton() {
    return GestureDetector(
      onTap: onUnfollow,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
        decoration: BoxDecoration(
          border: Border.all(color: ResumeTheme.primaryColor, width: 1),
          borderRadius: BorderRadius.circular(16.r),
        ),
        child: Text(
          '取消关注',
          style: TextStyle(
            fontSize: 12.sp,
            color: ResumeTheme.primaryColor,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 格式化关注时间
  String _formatCollectTime(String collectTime) {
    if (collectTime.isEmpty) return '未知';

    try {
      // 尝试解析时间字符串
      final dateTime = DateTime.tryParse(collectTime);
      if (dateTime != null) {
        return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}';
      }
    } catch (e) {
      // 解析失败，返回原始字符串
    }

    return collectTime;
  }
}