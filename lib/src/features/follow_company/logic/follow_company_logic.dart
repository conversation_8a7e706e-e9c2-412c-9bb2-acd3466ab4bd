
import 'package:flutter/material.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import '../../../base/base.dart';
import '../../../datasource/repositories/follow_company_repository.dart';
import '../data/follow_company_data_source.dart';

/// 🔥 关注企业页面业务逻辑 - 遵循项目架构规范
///
/// 功能特性：
/// - ✅ 继承ViewStateLogic，符合项目架构
/// - ✅ 集成CustomListLogic进行列表管理
/// - ✅ 使用FollowCompanyDataSource进行数据获取
/// - ✅ 支持分页加载和下拉刷新
/// - ✅ 统一的错误处理和状态管理
class FollowCompanyLogic extends ViewStateLogic {
  final FollowCompanyRepository repository;
  late CustomListLogic<CollectEnterpriseEntity> listLogic;
  late CustomListDataSource<CollectEnterpriseEntity> dataSource;

  // 🔥 状态跟踪 - 区分刷新和加载更多
  bool _wasRefreshing = false;
  bool _wasLoadingMore = false;

  FollowCompanyLogic({required this.repository}) {
    _initializeDataSource();
    _initializeListLogic();
  }

  /// 初始化数据源
  void _initializeDataSource() {
    dataSource = FollowCompanyDataSource(
      repository: repository,
      pageSize: 20,
    );
  }

  /// 初始化列表逻辑
  void _initializeListLogic() {
    listLogic = CustomListLogic<CollectEnterpriseEntity>(
      dataSource: dataSource,
    );

    // 监听列表状态变化，同步到当前Logic
    listLogic.addListener(_onListStateChanged);
  }

  /// 列表状态变化监听
  void _onListStateChanged() {
    // 同步ViewState
    viewState = listLogic.viewState;

    // 🔥 检测刷新状态变化（仅用于状态跟踪）
    // 刷新状态变化已由CustomListView内部处理，这里只做状态同步

    // 🔥 检测加载更多状态变化（不触发刷新成功消息）
    if (listLogic.isLoadingMore && !_wasLoadingMore) {
      // 开始加载更多 - 可以在这里添加加载更多开始回调
    } else if (!listLogic.isLoadingMore && _wasLoadingMore) {
      // 加载更多结束 - 可以在这里添加加载更多完成回调
    }

    // 🔥 更新状态跟踪
    _wasRefreshing = listLogic.isRefreshing;
    _wasLoadingMore = listLogic.isLoadingMore;

    notifyListeners();
  }

  // Getters - 兼容性接口
  List<CollectEnterpriseEntity> get followCompanies => listLogic.items;
  bool get hasMore => listLogic.hasMore;
  int get totalCount => listLogic.totalCount;
  bool get isRefreshing => listLogic.isRefreshing;
  bool get isLoadingMore => listLogic.isLoadingMore;

  @override
  void loadData() {
    listLogic.loadData();
  }

  @override
  void refreshPaging() {
    listLogic.refreshPaging();
  }

  @override
  void loadMorePaging() {
    listLogic.loadMorePaging();
  }

  /// 兼容性方法
  Future<void> fetchFollowCompanies() async {
    loadData();
  }

  /// 🔥 取消关注企业
  Future<void> unfollowCompany(String enterpriseId) async {
    try {
      // TODO: 实现取消关注的API调用
      // final response = await repository.unfollowCompany(enterpriseId);
      // if (response.isSuccess) {
      //   // 从列表中移除该企业
      //   listLogic.removeItem(item);
      // }

      debugPrint('🔍 FollowCompanyLogic - 取消关注企业: $enterpriseId');

      // 🔥 暂时模拟成功，刷新列表
      refreshPaging();

    } catch (e) {
      debugPrint('🔍 FollowCompanyLogic - 取消关注失败: $e');
    }
  }

  @override
  void dispose() {
    listLogic.removeListener(_onListStateChanged);
    listLogic.dispose();
    super.dispose();
  }
}