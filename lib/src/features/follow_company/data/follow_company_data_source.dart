import 'package:flutter_kit/src/base/core/base_result.dart';
import 'package:flutter_kit/src/datasource/models/collect_enterprise_entity.dart';
import 'package:flutter_kit/src/shared/components/custom_list_view/custom_list_view_export.dart';
import '../../../datasource/repositories/follow_company_repository.dart';

/// 🎯 关注企业列表数据源
///
/// 功能特性：
/// - ✅ 继承ApiListDataSource，符合项目架构
/// - ✅ 封装FollowCompanyRepository的调用
/// - ✅ 支持分页加载和数据验证
/// - ✅ 统一的错误处理和重试机制
/// - ✅ 数据转换和缓存支持
class FollowCompanyDataSource
    extends ApiListDataSource<CollectEnterpriseEntity> {
  final FollowCompanyRepository repository;

  FollowCompanyDataSource({
    required this.repository,
    super.pageSize = 20,
  }) : super(
          apiCall: (page, pageSize) =>
              _fetchFollowCompanyList(repository, page, pageSize),
          name: 'FollowCompanyDataSource',
        );

  /// 🔥 获取关注企业列表数据
  static Future<BaseResult<List<CollectEnterpriseEntity>>>
      _fetchFollowCompanyList(
    FollowCompanyRepository repository,
    int page,
    int pageSize,
  ) async {
    try {
      // 调用Repository获取数据
      final response = await repository.getCollectEnterpriseList();

      return response.when(
        success: (data, message) {
          return BaseResult.success(data);
        },
        failure: (message, code, error) {
          return BaseResult.failure(message);
        },
        empty: (message) {
          return BaseResult.empty();
        },
      );
    } catch (e) {
      return BaseResult.failure('获取关注企业列表失败: ${e.toString()}');
    }
  }

  @override
  String toString() {
    return 'FollowCompanyDataSource(pageSize: $pageSize, repository: $repository)';
  }
}
